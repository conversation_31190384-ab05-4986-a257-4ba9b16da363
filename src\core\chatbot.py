import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# <PERSON><PERSON>hain imports
from langchain_openai import Chat<PERSON>penAI, OpenAIEmbeddings
from langchain_community.vectorstores import FAISS

# Load environment variables from config directory
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env'))

# Import the chat history model
from src.models.chat_history import Chat<PERSON><PERSON><PERSON>

from src.core.text_to_cad_agent import TextToCADAgent, LOCAL_GUIDE_PATH, FAISS_INDEX_PATH

# Initialize LLMs
# Define model choices (should match those in text_to_cad_agent)
MODELS = {
    "default": "gpt-4.1-2025-04-14",  # Default model for general tasks
    "advanced": "o4-mini-2025-04-16", # Advanced model for complex tasks
    "expert": "o4-mini-2025-04-16",   # Expert model for specialized tasks
}

# Get API keys from environment variables
openai_api_key = os.getenv("OPENAI_API_KEY")
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")

# Initialize models
default_llm = None
advanced_llm = None
expert_llm = None

try:
    # Check if API key is available
    if not openai_api_key:
        raise ValueError("OPENAI_API_KEY environment variable is not set. Please check your .env file.")

    print("[INIT] Initializing language models...")

    # Initialize models with different reasoning efforts for different tasks
    default_llm = ChatOpenAI(
        model=MODELS["default"],
        temperature=0,  
        api_key=openai_api_key
    )

    advanced_llm = ChatOpenAI(
        model=MODELS["advanced"],
        reasoning_effort="medium",  
        api_key=openai_api_key
    )

    expert_llm = ChatOpenAI(
        model=MODELS["expert"],
        reasoning_effort="medium",
        api_key=openai_api_key
    )

    # Test connection with a simple query
    print("[TEST] Testing connection to OpenAI API...")
    test_result = default_llm.invoke("Test connection")
    print("[SUCCESS] Successfully connected to OpenAI API")

except ValueError as ve:
    print(f"[ERROR] Configuration Error: {ve}")
    print("   Please set the required API keys in your .env file.")

except Exception as e:
    print(f"[ERROR] Error initializing or connecting to LLM: {e}")
    print("   Please check your API key and network connection.")

    # Log more detailed error information for debugging
    import traceback
    print(f"   Error details: {traceback.format_exc()}")

# --- New RAG System Setup ---
# Import the new RAG retriever components
from src.rag.retriever import initialize_retriever, retrieve_context

# Initialize the RAG retriever system (loads FAISS index, CSVs, embedding model)
# This should be called once when the application starts.
try:
    print("\n--- Initializing New RAG System ---")
    initialize_retriever() 
    print("--- New RAG System Initialized ---")
    # The `retrieve_context` function will be used as the local_retriever
    # It's already imported, so we can pass it directly to TextToCADAgent
except Exception as e:
    print(f"[ERROR] Failed to initialize the new RAG system: {e}")
    print("   RAG functionalities will be unavailable.")
    # Define retrieve_context as a dummy function if initialization fails,
    # so TextToCADAgent doesn't break if it expects a callable.
    def retrieve_context(query: str, k_semantic: int = 5):
        print("Warning: RAG system not initialized. Returning empty context.")
        return []

# Initialize the TextToCADAgent
# Pass the initialized LLMs and the new RAG's retrieve_context function
text_to_cad_agent = TextToCADAgent(
    default_llm=default_llm, 
    advanced_llm=advanced_llm, 
    expert_llm=expert_llm, 
    local_retriever=retrieve_context # Pass the function itself
)

# Initialize chat history
chat_history = ChatHistory()

def process_user_request(user_message, is_edit_request=False, request_origin='unknown'):
    """
    Process the user message using the TextToCADAgent.

    This function serves as the main entry point for processing user requests.
    It handles both new requests and edit requests, and returns the generated
    code and GLTF file path.

    Args:
        user_message (str): The user's message/request
        is_edit_request (bool): Whether this is a request to edit existing code
        request_origin (str): The origin of the request ('web', 'api', or 'unknown')

    Returns:
        dict: A dictionary containing the generated/edited code and the GLTF file path,
              e.g., {"code": "...", "gltf_path": "..."}, or an error message.
    """
    # Log the incoming request
    print(f"\n{'='*80}")
    print(f"[REQUEST] New request received from {request_origin}")
    print(f"[MESSAGE] Message: '{user_message}'")
    print(f"[EDIT] Is edit request: {is_edit_request}")

    try:
        # Check if the agent is properly initialized
        if not text_to_cad_agent:
            error_msg = "Text-to-CAD agent not initialized properly. Check API keys and connections."
            print(f"[ERROR] {error_msg}")
            return {
                "error": error_msg,
                "code": None,
                "gltf_path": None
            }

        # Process the request using the agent
        print(f"[PROCESS] Processing request using TextToCADAgent...")
        result = text_to_cad_agent.process_request(user_message, is_edit_request, request_origin)

        # Validate the result
        if not isinstance(result, dict):
            error_msg = f"Unexpected response from agent: {result}"
            print(f"[ERROR] {error_msg}")
            return {
                "error": error_msg,
                "code": None,
                "gltf_path": None
            }

        # Save to chat history if code was generated successfully
        if result and "code" in result and result["code"]:
            chat_history.add_entry(user_message, result["code"], is_edit_request)

        # Log the result
        if "error" in result and result["error"]:
            print(f"[ERROR] Error in agent response: {result['error']}")
        elif "code" in result and result["code"]:
            print(f"[SUCCESS] Successfully generated code ({len(result['code'])} characters)")
            if "gltf_path" in result and result["gltf_path"]:
                print(f"[SUCCESS] Generated GLTF file: {result['gltf_path']}")
        elif "message" in result:
            print(f"[INFO] Agent is requesting more information: {result['message'][:100]}...")

        print(f"{'='*80}\n")
        return result

    except Exception as e:
        # Handle unexpected errors
        import traceback
        error_traceback = traceback.format_exc()
        error_msg = f"An error occurred while processing your request: {str(e)}"

        print(f"[ERROR] Error in process_user_request: {e}")
        print(f"Traceback: {error_traceback}")

        return {
            "error": error_msg,
            "code": None,
            "gltf_path": None
        }

# Removed generate_system_prompt and call_model functions
# Removed shape_chatbot function and __main__ block

# Functions to access chat history
def get_chat_history(limit=None):
    """
    Get chat history entries.

    Args:
        limit (int, optional): Maximum number of entries to return. If None, returns all entries.

    Returns:
        list: List of chat history entries.
    """
    if limit:
        return chat_history.get_recent_history(limit)
    else:
        return chat_history.get_all_history()

def clear_chat_history():
    """
    Clear all chat history.

    Returns:
        bool: True if successful.
    """
    chat_history.clear_history()
    return True
