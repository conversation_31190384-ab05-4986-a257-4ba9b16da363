# -*- coding: utf-8 -*-
# Text-to-CAD Agent Module: Convert text descriptions to FreeCAD code
# Using LangChain and LLMs to analyze requirements and generate FreeCAD code

import os
import sys
import json
import re
import subprocess
import csv # Added import
import functools # Added for lru_cache
from pathlib import Path
from typing import List, Optional, Dict, Any

from src.utils import path_manager # Added import

# Lang<PERSON>hain imports
from langchain_core.runnables import <PERSON><PERSON><PERSON>Lambda, RunnableParallel
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.pydantic_v1 import BaseModel, Field
from langchain_core.documents import Document

# Configure UTF-8 encoding
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8')

# Removed environment variable loading and API key checks

# Define model choices (kept for reference, actual models passed in)
MODELS = {
    "default": "o4-mini-2025-04-16",
    "advanced": "o4-mini-2025-04-16",
    "expert": "o4-mini-2025-04-16",
}

# --- Local RAG Setup Paths (Adjusted) ---
# Assuming 'data' and 'faiss_guide_index' are at the project root
LOCAL_GUIDE_PATH = "../../data/guide_en.txt"
FAISS_INDEX_PATH = "../../faiss_guide_index"
# local_retriever will be passed in

# Removed LLM initialization and RAG setup code

# Pydantic models for structured output
class ShapeRequirement(BaseModel):
    shape_type: str
    dimensions: Dict[str, Optional[float]]
    position: Optional[List[Optional[float]]] = None
    rotation: Optional[List[Optional[float]]] = None

class ExtractedShapeInfo(BaseModel): # New Pydantic model for LLM-based extraction
    shape_class: Optional[str] = Field(None, description="The general class or category of the primary shape, if identifiable (e.g., 'Perforated sheet', 'Tole'). Should be one of the known classes. Null if not identifiable.")
    shape_model_code: Optional[str] = Field(None, description="A specific model code or identifier associated with the shape_class, if provided by the user (e.g., 'DFM', 'ABC-123'). Null if not provided or not applicable.")

class Operation(BaseModel):
    operation_type: str = Field(description="Boolean operation type (cut, fuse, common)")
    base_shape: str = Field(description="Name of the base shape")
    tool_shape: str = Field(description="Name of the tool shape")
    result_name: str = Field(description="Name of the result after the operation")

class DesignRequirements(BaseModel):
    title: str = Field(description="Brief title describing the design")
    shapes: List[ShapeRequirement] = Field(description="List of required shapes")
    operations: Optional[List[Operation]] = Field(None, description="List of Boolean operations to perform")
    comments: Optional[str] = Field(None, description="Additional comments or instructions")
    complexity_level: int = Field(description="Design complexity level (1-5)")
    shape_class: Optional[str] = Field(None, description="The general class or category of the primary shape, if identifiable (e.g., 'Perforated sheet', 'Tole'). This is used to locate a specific parameter dictionary.")
    shape_model_code: Optional[str] = Field(None, description="A specific model code or identifier within the shape_class, if provided by the user (e.g., 'ABC', 'XYZ-123'). This is used as a key in the parameter dictionary.")

# Pydantic model for combined analysis and parameter check output
class AnalysisAndParameterCheckOutput(DesignRequirements):
    missing_info: bool = Field(description="Whether there is missing information to proceed with CAD generation.")
    questions: List[str] = Field(default_factory=list, description="List of questions to ask the user to get missing information.")
    explanation: Optional[str] = Field(None, description="Brief explanation about missing information, if any.")

# Define templates for each chain
unified_analysis_and_parameter_check_template = """You are a CAD expert. Your task is to analyze a user's description for a 3D model, extract design requirements, and determine if any critical information is missing for CAD generation.

User description/request:
{user_text}

Retrieved Context (from local guide, if available):
{retrieved_context}

Previous user responses (if any, to avoid re-asking):
{previous_responses}

Specific guidance based on identified model (if any, from dictionary.csv):
{dynamic_guidance}

CRITICAL NOTATION FOR PERFORATED SHEETS:
When a user requests a perforated sheet with notation like "C25 U30" or "R25 U30":
- "C" prefix ALWAYS means SQUARE holes (C for Carré/Square)
- "R" prefix ALWAYS means ROUND/CIRCULAR holes (R for Round)
- "U" ALWAYS refers to the pitch (center-to-center distance) between holes
- "T" ALWAYS Staggered Grid Pattern
Definition and formular(If the user provides any two parameters (diameter, pitch, or percentage open area), automatically calculate the third parameter without asking for it.):
- For circular hole staggered grid partern: percentage open area = 90.6*(diameter/pitch)^2
- For square hole straight grid partern: percentage open area =  100*(diameter/pitch)^2
Special case:
- Names like LR10x50 Z19x60 are OBROUND holes (rectangular with semicircular ends)
- Names like Ellipse EVL 15x30 are ELLIPTICAL holes
- Names like CD10 M30 are DIAMOND-shaped holes

DEFAULT UNITS:
- All dimensions are assumed to be in millimeters (mm) unless explicitly specified otherwise by the user.
- If the user provides a dimension without a unit (e.g., "sphere 10" or "hole 10"), assume millimeters.

**Part 1: Analyze Design Requirements**
Analyze the user's description and extract the following information:
1.  A brief title for the design.
2.  Required shapes (box, cylinder, sphere, cone, etc.).
3.  Dimensions for each shape (e.g., length, width, height, radius).
4.  Relative position of shapes ([x, y, z]). IMPORTANT: These must be numerical float values (e.g., [10.0, 5.5, 0.0]). If position is not specified or should be default/origin, use `null` or explicitly `[0.0, 0.0, 0.0]`. Do NOT use non-numeric placeholders like ["auto", "auto", 0].
5.  Rotation angles of shapes ([xrot, yrot, zrot] in degrees). IMPORTANT: These must be numerical float values. If rotation is not specified or default, use `null` or `[0.0, 0.0, 0.0]`.
6.  Boolean operations to perform (cut, fuse, common), including base shape, tool shape, and result name.
7.  Any additional comments or instructions.
8.  Design complexity level (1-5, where 5 is most complex).
9.  **Shape Classification**:
    - Identify if the user specifies a known shape category (e.g., 'Perforated sheet', 'Tole'). If so, populate `shape_class` with the identified category name (this should match a folder name under `data/Class/`).
    - If a `shape_class` is identified, look for an associated model code or identifier (e.g., 'ABC', 'DFM', 'XYZ-123') mentioned in proximity to the shape class. Populate `shape_model_code` with this identifier.
    - If no specific model code is found but a shape class is identified, `shape_model_code` can be null.
    - If no specific shape class is mentioned, both `shape_class` and `shape_model_code` should be null.

**Part 2: Check for Missing Information**
Based on the analyzed requirements and the `{dynamic_guidance}`:
1.  Determine if any critical information is missing to generate the 3D model.
2.  If information is missing, list specific, concise parameters to ask the user for. Format the `questions` list as a single string with all parameters on one line like "Missing parameters: Sheet width, Sheet length, Sheet thickness, ..." Each parameter should be 4 words or less, max 7 parameters.
3.  Provide a brief explanation if missing information.

**PRIORITY FOR MISSING INFO:**
- If `{dynamic_guidance}` lists required parameters, your questions **MUST** focus on those specific parameters if they are not already provided in the "User request" or "Previous user responses".
- If `{dynamic_guidance}` is empty or doesn't list specific parameters, consider general missing information (dimensions, positions, rotations, boolean op details).

**IMPORTANT GUIDELINES FOR MISSING INFO CHECK (Apply in this order of precedence):**
1.  **Re-ask Unanswered Prior Questions**: If *you* (the AI model) asked specific questions in your *immediately preceding "questions" list*, and the user's current response does not address all of those specific questions, you **MUST** set `missing_info` to `true` and include those *exact, unanswered questions* from your prior list in the new "questions" list. This rule takes precedence over leniency or other general guidelines, unless the user explicitly says to skip or proceed.
2.  **For Perforated Sheets**: Always ensure all required parameters are collected: sheet dimensions (length, width, thickness), hole shape, hole size (diameter/dimensions), hole spacing (pitch), and hole pattern type (straight grid/staggered). Set `missing_info` to `true` if any of these are missing.
3.  **User Skip Request**: If the user's message contains phrases like "don't ask anymore", "no more questions", "stop asking", "proceed anyway", ALWAYS set "missing_info" to false, regardless of actual missing information.
4.  **Perforated Sheet Specifics**: For "Perforated sheet" or similar objects, the key parameters to check for (if not already answered or covered by rule #1) are: sheet dimensions (length, width, thickness), hole shape (e.g., round, square, C/R notation), hole size (e.g., diameter, side length), hole pitch (spacing), and **hole pattern type** (e.g., straight, staggered, grid pattern).
5.  **General Missing Info & Leniency**: Consider all "Previous user responses" to avoid re-asking questions already answered. If the user has already provided at least one response (and rules #1 and #2 do not dictate otherwise), be more lenient and only ask for truly critical missing details, especially those highlighted by `{dynamic_guidance}` or general CAD requirements (dimensions, positions, etc.).

Return a single JSON structure strictly following this format:
```json
{{
  "title": "Brief description of the design",
  "shapes": [
    {{
      "shape_type": "box|cylinder|sphere|cone",
      "dimensions": {{"length": 10, "width": 20, "height": 5}} or {{"radius": 15, "height": 30}},
      "position": [x, y, z],
      "rotation": [xrot, yrot, zrot]
    }}
  ],
  "operations": [
    {{
      "operation_type": "cut|fuse|common",
      "base_shape": "base shape name",
      "tool_shape": "tool shape name",
      "result_name": "result name"
    }}
  ],
  "comments": "Additional comments or instructions",
  "complexity_level": 1-5,
  "shape_class": "e.g., Perforated sheet (nullable)",
  "shape_model_code": "e.g., ABC (nullable)",
  "missing_info": true/false,
  "questions": ["Question 1", "Question 2", ...],
  "explanation": "Brief explanation about missing information"
}}
```

IMPORTANT: Return only JSON, no explanations outside the JSON structure. Ensure the JSON is valid.
If no information is missing, "missing_info" should be false, and "questions" should be an empty list.
If the user describes a "sphere 10mm" or a "hole 10mm" without specifying a unit, assume the dimension is a radius of 10mm.
For highly complex designs (level 4-5), break down the design into multiple detailed shapes and operations.
"""

code_generation_template = """You are an expert FreeCAD scripter specializing in generating Python code to create highly detailed and complex 3D models based on design requirements.

**Analyzed design requirements:**
{design_requirements}

**Retrieved Context (from local guide):**
{retrieved_context}
You are an expert FreeCAD scripter specializing in generating Python code to create highly detailed and complex 3D models based on design requirements, optimized for execution in FreeCAD's command-line mode (`freecadcmd`).

**Analyzed design requirements:**
```json
{design_requirements}
Retrieved Context (from local guide):

{retrieved_context}
FreeCAD Workbench Guide Summary (Based on provided guide.txt):

Part Workbench: Core for 3D modeling. Use for basic shapes (box, cylinder, sphere, cone, torus, wedge, prism, helix), boolean operations (cut, fuse, common, section), complex shapes (loft, sweep, extrusion, revolution, shell, solid, compound, filled face, offset, thickness), and curves/lines (circle, ellipse, polygon, spline, bspline, bezier). Commands typically start with Part.. Boolean operations are performed on Shape objects (e.g., obj.Shape.cut, obj.Shape.fuse).
PartDesign Workbench: Feature-based modeling. Use for sketch-based features (pad, pocket, revolution, groove), dress-up features (fillet, chamfer, draft, thickness), patterns (linear, polar, multi-transform, scaled, mirrored), and advanced features (loft, pipe, additive/subtractive operations). Commands typically start with PartDesign.. Requires a PartDesign.Body.
Draft Workbench: Basic 2D/3D drawing and modification. Use for points, lines, wires, bsplines, bezier curves, circles, ellipses, rectangles, polygons, text, shape strings, and 3D operations (extrude, move, rotate, scale, offset). Commands typically start with Draft..
Curve Workbench (Addon): Advanced curve manipulation. Use for blend curves, parametric curves, bspline approximations, curves on surfaces, pipeshells, sweeps. Commands may start with Curve..
Surface Workbench (Addon): Advanced surface modeling. Use for bspline/bezier surfaces, extrusion/revolved/loft/swept surfaces, blend surfaces, filling faces, curve network surfaces. Commands may start with Surface..
Mesh Workbench: For mesh data (e.g., STL imports). Use for creating mesh primitives, converting shapes to meshes, mesh repair (flip/harmonize normals), smoothing, refining, exporting meshes (mesh.export). Commands typically start with Mesh..
Points Workbench (Addon): For point clouds. Use for creating/importing/exporting point clouds, converting to splines. Commands may start with Points..
Robot Workbench: For robot simulation. Use for creating robots, trajectories. Commands typically start with Robot..
Assembly Workbench (A2plus, Assembly3/4): For assembling parts. Syntax varies by workbench. Involves creating assemblies, adding parts, and defining constraints.
Arch Workbench: For architectural modeling. Use for walls, structures (beams/columns), roofs, floors, buildings, sites, windows, doors, pipes, stairs, rebar. Commands typically start with Arch.. Often builds on Draft objects.
Path Workbench: For CNC path generation (CAM). Use for toolpaths (profiles, pockets, drilling) based on geometry. Commands typically start with Path..
OpenSCAD Workbench: For OpenSCAD interaction. Use for polyhedrons, implicit functions, resizing. Commands typically start with OpenSCAD..
General Utilities: Use App.Vector, App.Placement, App.Rotation for positioning. Use Import module for file I/O (e.g., STEP export).
When generating code, select the most appropriate workbench and commands based on the design requirements and this guide. For example, use PartDesign for sketch-based modeling, Part for direct solid modeling and boolean operations, Draft for 2D elements or simple 3D arrangements, Arch for building elements. Ensure strict compatibility with FreeCAD 1.0.0 API and freecadcmd environment (no GUI dependencies).

Task: Generate a complete and executable Python script for FreeCAD that accurately models the object described in the analyzed design requirements, leveraging insights from the retrieved context and workbench guide for complex features or techniques. The script must be compatible with freecadcmd, avoiding any GUI-related commands (e.g., FreeCADGui, ViewObject.Visibility). Prioritize the design requirements, using the context and guide for clarification or advanced methods when applicable.

Mandatory requirements for the generated Python code:

Imports:

Always start with import FreeCAD as App and import Part.
Import math if mathematical calculations (e.g., trigonometry, constants) are needed.
Do not import FreeCADGui or use GUI-related commands (e.g., ViewObject.Visibility), as they are not supported in freecadcmd.
Import Draft if using Draft workbench tools (e.g., Draft.makeWire, Draft.makePolygon, Draft.makeShapeString).
Import Sketcher and PartDesign for complex sketch-based modeling when needed.
Import Import for STEP export (e.g., Import.export).
Import Mesh for OBJ export (e.g., Mesh.export).
Document Handling:

Create a new document: doc = App.newDocument("GeneratedModel"). Use doc = App.ActiveDocument only if instructed to modify an existing document.
Set document label if relevant: doc.Label = "DescriptiveName".
Shape Creation:

Use precise measurements for primitives (e.g., Part.makeBox, Part.makeCylinder).
Break down complex shapes into simpler components for accuracy.
Advanced Shape Tools:

Use Part.BSplineCurve and Part.BSplineSurface for complex curved surfaces.
Implement Part.makeShell and Part.makeSolid for complex enclosures.
Use Part.makeFillet and Part.makeChamfer for edge treatments.
Implement Part.makeThickness for hollow objects with precise wall thickness.
Use Part.makeOffsetShape for high-precision offset surfaces.
Boolean Operations:

For Part workbench, perform boolean operations on Shape objects:
Use obj.Shape.cut(tool_shape) for subtraction.
Use obj.Shape.fuse(tool_shape) for union.
Use obj.Shape.common(tool_shape) for intersection.
Create Part::Feature objects for each shape (e.g., obj = doc.addObject("Part::Feature", "Name"); obj.Shape = shape).
Update the base object's Shape after each boolean operation (e.g., base_obj.Shape = cut_shape).
Sequence operations carefully to maintain model integrity.
Use Part.Compound or Part.CompSolid for complex assemblies when appropriate.
High-Detail Features:

Implement threads, knurling, and patterned surfaces using mathematical formulas.
Create arrays of features (circular or linear patterns) for repeating elements.
Implement complex surface blends for smooth transitions.
Use compound paths and multi-stage boolean operations for intricate details.
Implement texturing or surface patterns where appropriate.
Positioning and Alignment:

Use precise coordinates with clear reference points (e.g., App.Vector(x, y, z)).
Implement parametric relationships using variables.
Use FreeCAD.Placement with accurate rotation matrices (e.g., obj.Placement = App.Placement(App.Vector(x, y, z), App.Rotation(yaw, pitch, roll))).
Implement constraints for PartDesign or Assembly workbenches when needed.
Optimization and Performance:

Store intermediate results in variables to avoid recalculation.
Group related operations into logical functions for clarity.
Check for problematic operations (e.g., invalid shapes) early.
Use appropriate tolerances for curved surfaces and complex operations (e.g., Part.Precision).
**IMPORTANT: Always automatically align the edges evenly.**
When creating objects with multiple holes or perforations:
1. ALWAYS create the entire base object first
2. Then create ALL hole shapes at once (store them in a list)
3. Finally perform ALL cutting operations in a single step using a compound of all holes
   Example:
   ```python
   # Create base object
   base = Part.makeBox(100, 100, 10)

   # Create all holes at once
   holes = []
   for x in range(10, 90, 20):
       for y in range(10, 90, 20):
           hole = Part.makeCylinder(5, 20, App.Vector(x, y, -5))
           holes.append(hole)

   # Create a compound of all holes
   compound_holes = Part.makeCompound(holes)

   # Cut all holes at once
   result = base.cut(compound_holes)
   ```
4. ALWAYS maintain equal margins between any holes/features and all edges of the created object.
5. NEVER place holes touching or too close to edges - ensure sufficient margin between holes and all edges.
This approach is significantly more efficient than creating and cutting each hole individually.
Documentation and Parameterization:

Define key dimensions as variables at the script's top for easy modification.
Include detailed comments for complex geometry, boolean operations, and positioning.
Structure code in logical sections (setup, base geometry, features, final operations).
Document mathematical formulas or algorithms used.
Error Prevention:

Ensure shapes are valid before boolean operations (e.g., check for non-empty shapes).
Avoid unsupported methods (e.g., Part.cut); use Shape methods or PartDesign features.
Verify compatibility with FreeCAD 1.0.0 API (e.g., use obj.Shape.cut over deprecated methods).
Ensure export file paths are valid and writable to prevent export failures.
Finalizing the Model:

Call doc.recompute() after significant operations (e.g., boolean operations, feature creation).
Add a final doc.recompute() before exporting to ensure model integrity.
Do not set display properties (e.g., obj.ViewObject.Visibility), as they are not supported in freecadcmd.
Export to STEP:

# IMPORTANT: Construct the ABSOLUTE path for the STEP file export.
# The output directory is one level *above* the script's assumed location.
# Example: If script is in 'F:/DFM_Engineering/cad_outputs_generated/', STEP should be in 'F:/DFM_Engineering/cad_outputs_generated/'.
output_dir_abs = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated')) # Go up one level from script location
os.makedirs(output_dir_abs, exist_ok=True) # Ensure directory exists
step_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.step')
Import.export([final_object], step_filename_abs) # Use the absolute path
print(f"Model exported to {{step_filename_abs}}") # Print the absolute path - Use double braces to escape

# Export to OBJ using Mesh workbench
obj_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.obj')
Mesh.export([final_object], obj_filename_abs) # Use the absolute path
print(f"Model exported to {{obj_filename_abs}}") # Print the absolute path - Use double braces to escape

Ensure Import and Mesh are imported.
Note:

The generated Python script itself will also be saved with a filename based on the design requirements title.
The script must be executable in freecadcmd without errors, avoiding any GUI dependencies.

CRITICAL: THROUGH HOLE GENERATION RULES

When creating holes that need to go THROUGH an object (through holes):

1. **Cylinder Positioning for Through Holes:**
   - Position cylinder to START BEFORE the object and END AFTER the object
   - For hole through X-axis: cylinder_base = App.Vector(-tolerance, center_y, center_z)
   - For hole through Y-axis: cylinder_base = App.Vector(center_x, -tolerance, center_z)
   - For hole through Z-axis: cylinder_base = App.Vector(center_x, center_y, -tolerance)

2. **Cylinder Length for Through Holes:**
   - Make cylinder LONGER than the dimension it's cutting through
   - Add tolerance on both sides: cylinder_length = object_dimension + (2 * tolerance)
   - Use tolerance = 1.0mm minimum

3. **Example - Hole through X-axis of box:**
   ```python
   # ✅ CORRECT: Through hole in X direction
   tolerance = 1.0
   hole_center_y = box_width * 0.5
   hole_center_z = box_height * 0.5

   # Start cylinder BEFORE box, extend BEYOND box
   cylinder_base = App.Vector(-tolerance, hole_center_y, hole_center_z)
   cylinder_length = box_length + (2 * tolerance)  # Longer than box
   cylinder = Part.makeCylinder(hole_radius, cylinder_length, cylinder_base, App.Vector(1, 0, 0))
   ```

4. **Example - Hole through Z-axis (top to bottom):**
   ```python
   # ✅ CORRECT: Through hole in Z direction
   tolerance = 1.0
   hole_center_x = box_length * 0.5
   hole_center_y = box_width * 0.5

   # Start cylinder BELOW box, extend ABOVE box
   cylinder_base = App.Vector(hole_center_x, hole_center_y, -tolerance)
   cylinder_length = box_height + (2 * tolerance)
   cylinder = Part.makeCylinder(hole_radius, cylinder_length, cylinder_base, App.Vector(0, 0, 1))
   ```

5. **Verification:**
   - Through hole should create FEWER faces than original object
   - Original box: 6 faces → Box with through hole: 5 faces (if hole goes completely through)
   - If faces count increases, the hole is NOT going through properly

ALWAYS ensure holes go COMPLETELY THROUGH the object when requested.

Output Format:
Return only the generated Python code. The code must be complete, highly detailed, executable in FreeCAD 1.0.0 freecadcmd without modifications, and free of errors such as incorrect API calls (e.g., Part.cut) or GUI-related commands (e.g., ViewObject.Visibility).

"""

# Define helper functions
def parse_unified_analysis(json_str: str) -> AnalysisAndParameterCheckOutput:
    """Convert JSON string to AnalysisAndParameterCheckOutput Pydantic model"""
    try:
        # Clean up the JSON string if needed
        if "```json" in json_str:
            json_str = re.search(r'```json\s*(.*?)\s*```', json_str, re.DOTALL).group(1)

        data = json.loads(json_str)
        return AnalysisAndParameterCheckOutput(**data)
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON string in parse_unified_analysis: {e}")
        # Return a minimal valid object for JSON decoding errors
        return AnalysisAndParameterCheckOutput(
            title="Invalid JSON format received",
            shapes=[ShapeRequirement(shape_type="box", dimensions={"length": 10.0, "width": 10.0, "height": 10.0})], # Ensure floats
            complexity_level=1,
            missing_info=True,
            questions=["The design requirements were not in a valid JSON format. Can you describe it again?"],
            explanation=f"JSON decoding error: {e}"
        )
    except Exception as e: # Catches Pydantic validation errors and other exceptions
        print(f"Error converting JSON to AnalysisAndParameterCheckOutput (likely Pydantic validation): {e}")
        # Return a fallback object that still allows the raw JSON to be used downstream
        return AnalysisAndParameterCheckOutput(
            title="Potentially Incomplete Requirements (Parsing Issues)",
            shapes=[ShapeRequirement(shape_type="box", dimensions={"length": 10.0, "width": 10.0, "height": 10.0})], # Ensure floats
            operations=None,
            comments="Fallback due to parsing issues.",
            complexity_level=1,
            shape_class=None,
            shape_model_code=None,
            missing_info=True, # Assume info might be missing or misparsed
            questions=["Some details might be unclear due to formatting. Please review the generated code carefully, or try rephrasing your request if the result is not as expected."],
            explanation=f"Pydantic validation or other parsing error: {str(e)}"
        )

def parse_unified_output(json_str: str) -> AnalysisAndParameterCheckOutput:
    """Convert JSON string to AnalysisAndParameterCheckOutput Pydantic model"""
    try:
        # Clean up the JSON string if needed
        if "```json" in json_str:
            json_str = re.search(r'```json\s*(.*?)\s*```', json_str, re.DOTALL).group(1)

        data = json.loads(json_str)
        return AnalysisAndParameterCheckOutput(**data)
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON string in parse_unified_output: {e}")
        # Return a minimal valid object for JSON decoding errors
        return AnalysisAndParameterCheckOutput(
            title="Invalid JSON format received",
            shapes=[ShapeRequirement(shape_type="box", dimensions={"length": 10.0, "width": 10.0, "height": 10.0})], # Ensure floats
            complexity_level=1,
            missing_info=True,
            questions=["The design requirements were not in a valid JSON format. Can you describe it again?"],
            explanation=f"JSON decoding error: {e}"
        )
    except Exception as e: # Catches Pydantic validation errors and other exceptions
        print(f"Error converting JSON to AnalysisAndParameterCheckOutput (likely Pydantic validation): {e}")
        # Return a fallback object that still allows the raw JSON to be used downstream
        return AnalysisAndParameterCheckOutput(
            title="Potentially Incomplete Requirements (Parsing Issues)",
            shapes=[ShapeRequirement(shape_type="box", dimensions={"length": 10.0, "width": 10.0, "height": 10.0})], # Ensure floats
            operations=None,
            comments="Fallback due to parsing issues.",
            complexity_level=1,
            shape_class=None,
            shape_model_code=None,
            missing_info=True, # Assume info might be missing or misparsed
            questions=["Some details might be unclear due to formatting. Please review the generated code carefully, or try rephrasing your request if the result is not as expected."],
            explanation=f"Pydantic validation or other parsing error: {str(e)}"
        )

def clean_code(code_str: str) -> str:
    """Clean up the code string"""
    if "```python" in code_str:
        code_str = re.search(r'```python\s*(.*?)\s*```', code_str, re.DOTALL).group(1)
    return code_str.strip()

def create_rag_query(design_reqs: DesignRequirements) -> str:
    """Creates a focused RAG query based on analyzed design requirements."""
    shape_types = []
    if design_reqs.shapes:
        shape_types = list(set([s.shape_type for s in design_reqs.shapes]))

    operation_types = []
    if design_reqs.operations:
        operation_types = list(set([o.operation_type for o in design_reqs.operations]))

    query_parts = ["FreeCAD Python script"]
    if shape_types:
        query_parts.append(f"for creating {' and '.join(shape_types)}")
    if operation_types:
        query_parts.append(f"using operations like {' and '.join(operation_types)}")

    query = " ".join(query_parts)

    # Fallback if no shapes/operations identified
    if not shape_types and not operation_types:
        # Use title as a fallback, or a generic term if no title
        fallback_term = design_reqs.title if design_reqs.title else 'CAD modeling'
        return f"FreeCAD Python script for {fallback_term}"

    return query

# Helper function to process retrieved documents
def format_retrieved_context(docs: List[Document]) -> str: # Changed type hint to List[Document]
    """Formats the retrieved list of documents (from FAISS) into a single string."""
    if not docs:
        return "No relevant context found."

    context_str = ""
    for i, doc in enumerate(docs):
        if isinstance(doc, Document):
            content = doc.page_content
            source = doc.metadata.get('source', 'Local Guide') # Add source if available
            context_str += f"--- Context Source {i+1} ({source}) ---\n{content}\n\n"
        else:
             # This case should ideally not happen if the retriever returns Document objects
             print(f"Warning: Unexpected document type in format_retrieved_context: {type(doc)}")
             context_str += f"--- Context Source {i+1} (Unknown Source) ---\n{str(doc)}\n\n"


    return context_str.strip()

# Helper function to combine and format contexts
def combine_and_format_contexts(inputs: Dict[str, Any]) -> Dict[str, Any]:
    """Formats the local context."""
    # Only local context is expected now
    combined_docs = inputs.get("local_context", [])

    formatted_context = format_retrieved_context(combined_docs)

    # Sanitize title for filename - only allow ASCII characters
    # This ensures compatibility with various operating systems and prevents issues with special characters
    design_requirements = inputs["design_requirements"]
    # First remove non-alphanumeric, space, or hyphen characters, then filter out non-ASCII
    sanitized_title = re.sub(r'[^\w\s-]', '', design_requirements.title).strip()
    # Keep only ASCII characters
    sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128)
    # Replace spaces with underscores
    sanitized_title = sanitized_title.replace(' ', '_')
    if not sanitized_title:
        sanitized_title = "generated_cad" # Use a default if title is empty

    # Return a dictionary suitable for the next step (code_generation_prompt)
    return {
        "design_requirements": design_requirements,
        "retrieved_context": formatted_context,
        "sanitized_title": sanitized_title
        # "user_text": inputs["user_text"] # Pass through if needed later
    }


class TextToCADAgent:
    def __init__(self, default_llm, advanced_llm, expert_llm, local_retriever=None):
        """
        Initialize the TextToCADAgent with language models and retriever.

        Args:
            default_llm: The default language model for general tasks
            advanced_llm: The advanced language model for complex tasks
            expert_llm: The expert language model for specialized tasks
            local_retriever: Optional retriever for RAG functionality

        The agent uses different LLMs for different tasks:
        - default_llm: Used for simple tasks
        - advanced_llm: Used for requirements analysis and code generation
        - expert_llm: Used for parameter checking and code editing

        The local_retriever provides RAG functionality to enhance the agent's
        knowledge about CAD modeling and FreeCAD.
        """
        self.default_llm = default_llm
        self.advanced_llm = advanced_llm
        self.expert_llm = expert_llm
        self.local_retriever = local_retriever

        # Define constants (not instance state)
        self.MAX_QUESTION_ATTEMPTS = 5 # Max rounds of questions before proceeding

        # Initialize session state manager
        self._session_states = {}

        # Initialize templates
        self.code_generation_prompt = ChatPromptTemplate.from_template(code_generation_template)
        self.unified_analysis_prompt = ChatPromptTemplate.from_template(unified_analysis_and_parameter_check_template)
        self.code_editing_template = """You are an expert FreeCAD scripter specializing in modifying existing Python code to implement new features or changes based on user requests.

**Original Code:**
```python
{original_code}
```

**User Request:**
```
{user_request}
```

**Retrieved Context (from local guide):**
```
{retrieved_context}
```

Task: Modify the existing FreeCAD Python code to implement the user's requested changes. The modified code must remain compatible with freecadcmd, avoiding any GUI-related commands (e.g., FreeCADGui, ViewObject.Visibility).

CRITICAL: THROUGH HOLE GENERATION RULES (for modifications involving holes)

When creating holes that need to go THROUGH an object (through holes):

1. **Cylinder Positioning for Through Holes:**
   - Position cylinder to START BEFORE the object and END AFTER the object
   - For hole through X-axis: cylinder_base = App.Vector(-tolerance, center_y, center_z)
   - For hole through Y-axis: cylinder_base = App.Vector(center_x, -tolerance, center_z)
   - For hole through Z-axis: cylinder_base = App.Vector(center_x, center_y, -tolerance)

2. **Cylinder Length for Through Holes:**
   - Make cylinder LONGER than the dimension it's cutting through
   - Add tolerance on both sides: cylinder_length = object_dimension + (2 * tolerance)
   - Use tolerance = 1.0mm minimum

3. **Example - Hole through X-axis of box:**
   ```python
   # ✅ CORRECT: Through hole in X direction
   tolerance = 1.0
   hole_center_y = box_width * 0.5
   hole_center_z = box_height * 0.5

   # Start cylinder BEFORE box, extend BEYOND box
   cylinder_base = App.Vector(-tolerance, hole_center_y, hole_center_z)
   cylinder_length = box_length + (2 * tolerance)  # Longer than box
   cylinder = Part.makeCylinder(hole_radius, cylinder_length, cylinder_base, App.Vector(1, 0, 0))
   ```

ALWAYS ensure holes go COMPLETELY THROUGH the object when requested.

IMPORTANT: This template provides a variable called "{sanitized_title}" that MUST be defined in your generated code. You will see instructions below on how to include this variable in your code.

Guidelines for modifying the code:
1. Carefully analyze the existing code to understand its structure and the objects it creates.
2. Identify the appropriate location to add the requested modifications.
3. Maintain the same coding style and variable naming conventions.
4. Ensure all imports are preserved and add any new imports if needed (e.g., `import Mesh` if adding OBJ export).
5. Preserve the document creation and setup code.
6. Preserve the STEP export functionality at the end, ensuring it uses the correct ABSOLUTE path for export (see note below). Add OBJ export similarly if requested.
7. Add clear comments to explain your modifications.
8. Ensure the modified code is complete and executable in FreeCAD 1.0.0 freecadcmd.
9. When creating objects with multiple holes or perforations:
   a. ALWAYS create the entire base object first
   b. Then create ALL hole shapes at once (store them in a list)
   c. Finally perform ALL cutting operations in a single step using a compound of all holes
   Example:
   ```python
   # Create base object
   base = Part.makeBox(100, 100, 10)

   # Create all holes at once
   holes = []
   for x in range(10, 90, 20):
       for y in range(10, 90, 20):
           hole = Part.makeCylinder(5, 20, App.Vector(x, y, -5))
           holes.append(hole)

   # Create a compound of all holes
   compound_holes = Part.makeCompound(holes)

   # Cut all holes at once
   result = base.cut(compound_holes)
   ```
   This approach is significantly more efficient than creating and cutting each hole individually.

Output Format:
Return only the modified Python code. The code must be complete, executable in FreeCAD 1.0.0 freecadcmd without errors, and implement the requested changes while preserving the original functionality.

**STEP Export Path Note:** The STEP file MUST be exported using an absolute path to the `cad_outputs_generated` directory, which is located one level *above* the directory where this script will be saved.

IMPORTANT: You MUST define the sanitized_title variable in your code exactly as shown below. The value "{sanitized_title}" is provided to this template and should be used in your code:

```python
import os
# Define the sanitized_title variable with the exact value provided to this template
sanitized_title = "{sanitized_title}"  # DO NOT CHANGE THIS VALUE

# Then use it for the export path
output_dir_abs = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated'))
os.makedirs(output_dir_abs, exist_ok=True)
step_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.step')
# Ensure the final object variable (e.g., 'final_object', 'result_shape') is correct
Import.export([final_object], step_filename_abs) # Use the absolute path
print(f"Model exported to {{step_filename_abs}}") # Print the absolute path - Use double braces to escape

# Also export to OBJ using Mesh workbench
obj_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.obj')
Mesh.export([final_object], obj_filename_abs) # Use the absolute path
print(f"Model exported to {{obj_filename_abs}}") # Print the absolute path - Use double braces to escape
```
Ensure the final export calls use `step_filename_abs`, `obj_filename_abs`, and the correct final object variable. The sanitized_title variable MUST be defined in your code exactly as shown above. Ensure `Import` and `Mesh` are imported in the script.
"""

        # Initialize processing chains
        self.unified_processing_chain = RunnableLambda(
            lambda x: {
                # Prepare variables for the unified analysis prompt
                "user_text": x["user_text"],
                "previous_responses": x["previous_responses_formatted"],
                "dynamic_guidance": "" if not x["latest_requirements_for_guidance"] else str(x["latest_requirements_for_guidance"]),
                "retrieved_context": format_retrieved_context(
                    self.local_retriever(x["user_text"]) if self.local_retriever else []
                )
            }
        ) | RunnableParallel(
            {
                "unified_analysis": self.unified_analysis_prompt | self.expert_llm | StrOutputParser(),
                "template_inputs": lambda x: x  # Pass through the inputs for later use
            }
        ) | RunnableLambda(
            lambda x: {
                "unified_output_obj": parse_unified_analysis(x["unified_analysis"]),
                "raw_unified_json": x["unified_analysis"],
                "retrieved_context_for_code_gen": x["template_inputs"]["retrieved_context"]
            }
        )

        # RAG Code Generation Chain - Modified to accept pre-fetched context
        # Input: {"design_requirements_obj": AnalysisAndParameterCheckOutput, "raw_design_requirements_json": str, "retrieved_context": str}
        self.rag_code_generation_chain = (
            RunnableLambda(
                lambda x: {
                    # Filter the design requirements object to include only code-generation relevant fields
                    "design_requirements": json.dumps({
                        "title": x["design_requirements_obj"].title,
                        "shapes": [s.dict() for s in x["design_requirements_obj"].shapes] if x["design_requirements_obj"].shapes else [],
                        "operations": [o.dict() for o in x["design_requirements_obj"].operations] if x["design_requirements_obj"].operations else None,
                        "comments": x["design_requirements_obj"].comments,
                        "complexity_level": x["design_requirements_obj"].complexity_level,
                        # Explicitly exclude missing_info, questions, explanation, shape_class, shape_model_code
                    }, indent=2), # Convert the filtered dict to a JSON string
                    "retrieved_context": x["retrieved_context"], # Use the passed context
                    "sanitized_title": ( # This still needs the object for the title
                        lambda dr_obj: (
                            ''.join(c for c in re.sub(r'[^\w\s-]', '', dr_obj.title).strip() if ord(c) < 128).replace(' ', '_')
                            or "generated_cad"
                        )
                    )(x["design_requirements_obj"]) # Pass the DesignRequirements object to the lambda
                }
            )
            | self.code_generation_prompt
            | self.advanced_llm
            | StrOutputParser()
            | RunnableLambda(clean_code)
        )

        # Define the code editing chain
        self.code_editing_prompt = ChatPromptTemplate.from_template(self.code_editing_template)
        self.code_editing_chain = (
            {
                "original_code": lambda x: x["original_code"],
                "user_request": lambda x: x["user_request"],
                "retrieved_context": lambda x: x["retrieved_context"],
                "sanitized_title": lambda x: x["sanitized_title"]
            }
            | self.code_editing_prompt
            | self.expert_llm
            | StrOutputParser()
            | RunnableLambda(clean_code)
        )

    def _get_session_state(self, session_id):
        """Get the state for a specific session.

        Args:
            session_id (str): The unique identifier for the session

        Returns:
            dict: The session state dictionary
        """
        if session_id not in self._session_states:
            # Initialize a new session with default values
            self._session_states[session_id] = {
                'latest_code': None,
                'latest_title': None,
                'latest_requirements': None,
                'user_text_history': [],
                'conversation_state': 'initial',
                'pending_questions': [],
                'previous_responses': [],
                'initiating_query_for_collection': None
            }
        return self._session_states[session_id]

    def _update_session_state(self, session_id, **updates):
        """Update the state for a specific session.

        Args:
            session_id (str): The unique identifier for the session
            **updates: Keyword arguments with state updates

        Returns:
            dict: The updated session state
        """
        session_state = self._get_session_state(session_id)
        session_state.update(updates)
        return session_state




    def reset_conversation(self, session_id: Optional[str] = None):
        """
        Reset conversation state.
        If session_id is provided, resets only that session.
        If session_id is None (e.g., called from a global refresh), resets all sessions and instance variables.
        """
        if session_id:
            if session_id in self._session_states:
                print(f"[PROCESS] Resetting conversation state for session {session_id}")
                # Re-initialize the specific session state
                self._session_states[session_id] = {
                    'latest_code': None,
                    'latest_title': None,
                    'latest_requirements': None,
                    'user_text_history': [],
                    'conversation_state': 'initial',
                    'pending_questions': [],
                    'previous_responses': [],
                    'initiating_query_for_collection': None,
                    # Ensure edit-specific state is also reset for the session
                    'edit_request_history': [],
                    'edit_context': {}
                }
                print(f"[SUCCESS] Conversation reset complete for session {session_id}")
            else:
                print(f"[WARNING] Attempted to reset non-existent session: {session_id}")
        else:
            # Global reset: clear all session states and instance variables
            print("[PROCESS] Resetting all session states and instance-level fallback/template variables.")
            self._session_states.clear()

            # Reset instance variables (these might serve as templates or fallbacks if ever used without a session context)
            # These are less critical if all operations correctly use _session_states, but good for completeness.
            # self.user_text_history = [] # Instance-level, potentially for a default/global context if design allows
            # self.conversation_state = "initial" # Instance-level
            # self.pending_questions = [] # Instance-level
            # self.previous_responses = [] # Instance-level
            # self.latest_code = None # Instance-level
            # self.latest_title = None # Instance-level
            # self.latest_requirements = None # Instance-level
            # self.initiating_query_for_collection = None # Instance-level
            #
            # if hasattr(self, 'edit_request_history'):
            #     self.edit_request_history = [] # Instance-level
            # if hasattr(self, 'edit_context'):
            #     self.edit_context = {} # Instance-level
            print("[SUCCESS] All session states reset complete. Instance variables are not actively used for session data.")

    # Helper method to format previous responses for the prompt (session specific)
    def format_previous_responses_for_session(self, previous_responses_list: List[str]) -> str: # Renamed and clarified
        if not previous_responses_list:
            return "No previous responses for this session."

        formatted = "Previous responses for this session:\n"
        for i, response in enumerate(previous_responses_list):
            formatted += f"{i+1}. {response}\n"
        return formatted

    # def parse_parameter_check_result(self, result_str): # To be removed
    #     """Parse the parameter check result from JSON string to ParameterCheckResult object."""
    #     try:
    #         # Find JSON in the result
    #         json_match = re.search(r'```json\s*(.*?)\s*```', result_str, re.DOTALL)
    #         if json_match:
    #             json_str = json_match.group(1)
    #         else:
    #             # If no JSON block found, try to parse the entire string
    #             json_str = result_str
    #
    #         # Clean up the JSON string - remove any leading/trailing whitespace
    #         json_str = json_str.strip()
    #
    #         # Parse JSON
    #         result_dict = json.loads(json_str)
    #         return ParameterCheckResult(**result_dict)
    #     except Exception as e:
    #         print(f"Error parsing parameter check result: {e}")
    #         print(f"Raw result string: {result_str[:200]}...")  # Print first 200 chars for debugging
    #         # Return default result if error occurs
    #         return ParameterCheckResult( # This was for the old ParameterCheckResult
    #             missing_info=False,
    #             questions=[],
    #             explanation="Error parsing parameter check result"
    #         )

    def process_request(self, user_text, is_edit_request=False, request_origin='unknown', session_id=None):
        """
        Process user request and generate or edit FreeCAD code.

        Args:
            user_text (str): The user's message/request.
            is_edit_request (bool): Whether this is a request to edit existing code.
            request_origin (str): Origin of the request ('api', 'web', etc.)
            session_id (str, optional): Unique identifier for the user session. If not provided,
                                       a new random session ID will be generated.

        Returns:
            dict: A dictionary containing the generated/edited code, GLTF file path,
                  a message for the user, or an error message.
        """
        # Generate a session ID if not provided
        if session_id is None:
            import uuid
            import random
            rand_digits = random.randint(100000, 999999)
            rand_uuid = uuid.uuid4().hex[:6]
            session_id = f"session_{rand_uuid}_{rand_digits}"

        print(f"[DEBUG] Analyzing request: '{user_text}' for session {session_id}...")

        # Get session state
        state = self._get_session_state(session_id)

        # Update user text history
        state['user_text_history'].append(user_text)

        skip_questions_keywords = ["dont ask anymore", "no more questions", "stop asking",
                                  "proceed anyway", "just continue", "skip questions"]
        skip_questions = any(keyword in user_text.lower() for keyword in skip_questions_keywords)

        if skip_questions:
            print(f"[WARNING] User requested to skip further questions. Session: {session_id}")
            # Force proceed if in collection state
            if state['conversation_state'] == "collecting_info":
                # Use the full history to try and generate code
                return self.continue_information_collection(" ".join(state['user_text_history']), request_origin, session_id=session_id, force_proceed=True)
            elif state['conversation_state'] == "collecting_info_edit":
                 # Use the full history to try and edit code
                return self.continue_edit_information_collection(" ".join(state['user_text_history']), request_origin, session_id=session_id, force_proceed=True)
            # If not in collection state, but skip is requested, it implies a new request that should proceed without questions
            # This will be handled by process_new_request by not asking questions if skip_questions is true.

        if state['conversation_state'] in ["collecting_info", "collecting_info_edit"] and not skip_questions:
            state['previous_responses'].append(user_text)
            self._update_session_state(session_id, previous_responses=state['previous_responses'])
            print(f"[MESSAGE] Added user response to tracking: '{user_text}' for session {session_id}")
            combined_text = " ".join(state['user_text_history'])
            if is_edit_request and state['conversation_state'] == "collecting_info_edit":
                return self.continue_edit_information_collection(combined_text, request_origin, session_id=session_id)
            elif not is_edit_request and state['conversation_state'] == "collecting_info":
                return self.continue_information_collection(combined_text, request_origin, session_id=session_id)

        if is_edit_request and state['latest_code']:
            return self.process_edit_request(user_text, request_origin, session_id=session_id) # Edit requests might also need info collection
        else:
            # Pass skip_questions flag to process_new_request
            return self.process_new_request(user_text, request_origin, session_id=session_id, skip_questions_requested=skip_questions)

    def process_new_request(self, user_text, request_origin='unknown', session_id=None, skip_questions_requested=False):
        """
        Process a request to generate new FreeCAD code using the unified chain.

        Args:
            user_text (str): The user's request message
            request_origin (str): Origin of the request ('api', 'web', etc.)
            session_id (str, optional): Session identifier. If None, a new session will be created.
            skip_questions_requested (bool): Whether to skip asking questions even if info is missing

        Returns:
            dict: Response containing code, message, or error information
        """
        # Ensure we have a valid session ID
        if session_id is None:
            import uuid
            import random
            rand_digits = random.randint(100000, 999999)
            rand_uuid = uuid.uuid4().hex[:6]
            session_id = f"session_{rand_uuid}_{rand_digits}"

        # Get session state
        state = self._get_session_state(session_id)

        try:
            chain_input = {
                "user_text": user_text,
                "previous_responses_formatted": self.format_previous_responses_for_session(state['previous_responses']),
                "latest_requirements_for_guidance": state['latest_requirements'] # Pass previous analysis if available
            }
            unified_chain_result = self.unified_processing_chain.invoke(chain_input)

            # unified_output = unified_chain_result["unified_output"] # OLD
            unified_output_obj = unified_chain_result["unified_output_obj"] # NEW
            raw_unified_json = unified_chain_result["raw_unified_json"] # NEW
            retrieved_context_for_code_gen = unified_chain_result["retrieved_context_for_code_gen"]

            print(f"\n[SUCCESS] Unified analysis and parameter check successful for session {session_id}:")
            # print(f"Output: {json.dumps(unified_output.dict(), indent=2)}") # OLD
            print(f"Parsed Output: {json.dumps(unified_output_obj.dict(), indent=2)}") # NEW
            # print(f"Raw JSON Output: {raw_unified_json}") # Optional: for debugging

            # Store the requirements in session state
            self._update_session_state(session_id, latest_requirements=unified_output_obj)

            # Handle parsing error first
            if unified_output_obj.title == "Unable to parse requirements":
                print(f"[ERROR] Parsing error in process_new_request for session {session_id}: {unified_output_obj.explanation}")
                return {
                    "code": None, "gltf_path": None,
                    "message": f"I had trouble understanding your request. Could you please try rephrasing it? Details: {unified_output_obj.explanation}",
                    "explanation": unified_output_obj.explanation
                }

            if unified_output_obj.missing_info and unified_output_obj.questions and not skip_questions_requested:
                print(f"\n❓ Missing information detected for session {session_id}. Questions: {unified_output_obj.questions}")

                # Filter out questions already effectively answered (simple check)
                # More sophisticated NLP could be used here
                truly_pending_questions = []
                if state['previous_responses']:
                    for q_text in unified_output_obj.questions:
                        q_keywords = set(q_text.lower().replace('?', '').split())
                        answered = False
                        for prev_resp in state['previous_responses']:
                            resp_keywords = set(prev_resp.lower().split())
                            if len(q_keywords.intersection(resp_keywords)) > 1: # Simple overlap check
                                answered = True
                                break
                        if not answered:
                            truly_pending_questions.append(q_text)
                else:
                    truly_pending_questions = unified_output_obj.questions

                if not truly_pending_questions and not skip_questions_requested: # All questions seem answered
                     print(f"[SUCCESS] All questions appear to have been answered in previous responses or user skipped for session {session_id}.")
                     # Pass raw_unified_json to generate_code_from_requirements
                     return self.generate_code_from_requirements(unified_output_obj, raw_unified_json, retrieved_context_for_code_gen, request_origin, session_id=session_id)

                if truly_pending_questions and not skip_questions_requested:
                    # Update session state with the new conversation state
                    self._update_session_state(
                        session_id,
                        conversation_state="collecting_info",
                        pending_questions=truly_pending_questions,
                        initiating_query_for_collection=user_text # Store the query that led to questions
                    )

                    all_questions_str = "\n".join([f"{i+1}. {q}" for i, q in enumerate(truly_pending_questions)])
                    message_to_user = f"Please provide the following missing information to create your 3D model:\n\n{all_questions_str}"
                    if unified_output_obj.explanation:
                        message_to_user += f"\n\n{unified_output_obj.explanation}"

                    return {
                        "code": None, "gltf_path": None,
                        "message": message_to_user, "explanation": unified_output_obj.explanation
                    }

            # No missing info, or user requested to skip questions
            # Pass raw_unified_json to generate_code_from_requirements
            return self.generate_code_from_requirements(unified_output_obj, raw_unified_json, retrieved_context_for_code_gen, request_origin, session_id=session_id)

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"[ERROR] Error during unified new request processing for session {session_id}: {e}")
            print(f"Traceback: {error_traceback}")
            return {"error": f"Error analyzing requirements: {str(e)}", "code": None, "gltf_path": None}


    @functools.lru_cache(maxsize=None)
    def _get_dynamic_guidance_from_dictionary(self, shape_class: Optional[str], shape_model_code: Optional[str]) -> str:
        if not shape_class: # shape_model_code can be None if we are just looking for general class guidance
            return ""

        # If shape_model_code is None, we might still want to provide general guidance for the shape_class if available,
        # or this function could return "" if a specific model code is always required for guidance.
        # For now, if shape_model_code is None, we won't find a specific entry.
        if not shape_model_code:
            print(f"DEBUG: No shape_model_code provided for shape_class '{shape_class}'. No specific model guidance will be retrieved.")
            return ""

        dict_path = path_manager.DATA_DIR / "Class" / shape_class / "dictionary.csv"
        guidance = ""
        print(f"DEBUG: Attempting to read dictionary for dynamic guidance from: {dict_path}")

        if dict_path.exists():
            try:
                with open(dict_path, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    headers = next(reader, None)

                    if headers:
                        name_col_idx = -1
                        params_col_idx = -1
                        desc_col_idx = -1 # Added: Index for Description column

                        # Case-insensitive search for 'Name', 'Parameters', and 'Description' columns
                        for i, h_val in enumerate(headers):
                            h_lower = h_val.strip().lower()
                            if h_lower in ['name', 'id', 'code', 'model', 'modelcode', 'model_code']:
                                name_col_idx = i
                            elif h_lower in ['parameters', 'params', 'parameter']:
                                params_col_idx = i
                            elif h_lower in ['description', 'desc', 'detail', 'description']: # Check for description column names
                                desc_col_idx = i

                        # Default if not found by specific names
                        if name_col_idx == -1: name_col_idx = 0 # Default to first column for Name/ID
                        if params_col_idx == -1: params_col_idx = 1 # Default to second column for Parameters
                        # No default for description, it's optional

                        if name_col_idx == -1 or params_col_idx == -1 :
                             print(f"DEBUG: Could not reliably determine Name/ID and Parameters columns in {dict_path}")
                             return ""

                        actual_parameters_for_model = []
                        model_description = "" # Added: Variable to store description

                        for row_data in reader:
                            if row_data and \
                               len(row_data) > name_col_idx and \
                               len(row_data) > params_col_idx and \
                               row_data[name_col_idx].strip().upper() == shape_model_code.strip().upper():

                                params_string = row_data[params_col_idx].strip()
                                if params_string:
                                    actual_parameters_for_model = [p.strip() for p in params_string.split(',') if p.strip()]

                                # Added: Extract description if column exists and row has enough columns
                                if desc_col_idx != -1 and len(row_data) > desc_col_idx:
                                    model_description = row_data[desc_col_idx].strip()

                                break # Found the model, exit loop

                        guidance_parts = [] # Use a list to build the guidance string

                        if actual_parameters_for_model:
                            guidance_parts.append(
                                f"For the specified model '{shape_model_code}' of type '{shape_class}', "
                                f"the following parameters are typically required: {', '.join(actual_parameters_for_model)}. "
                                "Please provide them if you haven't already."
                            )
                            print(f"DEBUG: Dynamic params guidance generated.")

                        # Added: Include description if found
                        if model_description:
                             guidance_parts.append(f"Description for this model: {model_description}")
                             print(f"DEBUG: Dynamic description guidance generated.")

                        guidance = " ".join(guidance_parts).strip() # Join parts with space and strip whitespace

                        if not guidance:
                             print(f"DEBUG: Model code '{shape_model_code}' not found or has no parameters/description in {dict_path} (or parameters/description string was empty).")

                    else:
                        print(f"DEBUG: Dictionary file {dict_path} is empty or has no headers.")
            except FileNotFoundError:
                print(f"DEBUG: Dictionary file not found (though exists() was true?): {dict_path}") # Should not happen if dict_path.exists() is true
            except Exception as e:
                print(f"Error reading or parsing dictionary.csv for {shape_class}/{shape_model_code}: {e}")
        else:
            print(f"DEBUG: Dictionary file path does not exist: {dict_path}")

        return guidance

    # def process_new_request(self, user_text, request_origin='unknown'): # OLD version to be removed
    #     """
    #     Process a request to generate new FreeCAD code.
    #
    #     Args:
    #         user_text (str): The user's message/request
    #         request_origin (str): The origin of the request ('web', 'api', or 'unknown')
    #
    #     Returns:
    #         dict: A dictionary containing the generated code and the GLTF file path,
    #               or an error message.
    #     """
    #     # Step 1: Invoke the new unified chain
    #     try:
    #         chain_input = {
    #             "user_text": user_text,
    #             "previous_responses_formatted": self.format_previous_responses()
    #         }
    #         chain_result = self.analysis_and_parameter_check_chain.invoke(chain_input) # This was the OLD chain
    #
    #         design_requirements = chain_result["design_requirements_obj"]
    #         check_result = chain_result["parameter_check_result_obj"]
    #         # Retrieve the context from param check to pass to code generation
    #         context_from_param_check = chain_result["context_from_param_check"]
    #
    #         print(f"\n[SUCCESS] Unified analysis and parameter check successful:")
    #         print(f"Design Requirements: {json.dumps(design_requirements.dict(), indent=2)}")
    #         print(f"Parameter Check Result: {json.dumps(check_result.dict(), indent=2)}")
    #
    #         # Store the latest requirements
    #         self.latest_requirements = design_requirements
    #
    #         # If there's missing information, ask the user for more details
    #         if check_result.missing_info and check_result.questions:
    #             print(f"\n❓ Missing information detected. Questions: {check_result.questions}")
    #
    #             # Filter out questions that might have been answered in previous responses
    #             if self.previous_responses:
    #                 # This is a simplified approach - in a more sophisticated implementation,
    #                 # we would use NLP to determine if a question has been answered
    #                 filtered_questions = []
    #                 for question in check_result.questions:
    #                     # Check if any previous response contains keywords from this question
    #                     question_keywords = set(question.lower().split())
    #                     is_answered = False
    #                     for response in self.previous_responses:
    #                         response_words = set(response.lower().split())
    #                         # If there's significant overlap between question keywords and response
    #                         if len(question_keywords.intersection(response_words)) > 2:
    #                             is_answered = True
    #                             break
    #                     if not is_answered:
    #                         filtered_questions.append(question)
    #
    #                 if not filtered_questions:
    #                     print("[SUCCESS] All questions appear to have been answered in previous responses.")
    #                     return self.generate_code_from_requirements(design_requirements, request_origin) # Error: context_from_param_check missing
    #
    #                 check_result.questions = filtered_questions
    #
    #             # Set conversation state to collecting information
    #             self.conversation_state = "collecting_info"
    #
    #             # Combine all questions into a single message
    #             all_questions = "\n".join([f"{i+1}. {q}" for i, q in enumerate(check_result.questions)])
    #             combined_message = f"Please provide the following missing information to create your 3D model:\n\n{all_questions}"
    #
    #             if check_result.explanation:
    #                 combined_message += f"\n\n{check_result.explanation}"
    #
    #             # Store pending questions for reference
    #             self.pending_questions = check_result.questions
    #
    #             return {
    #                 "code": None,
    #                 "gltf_path": None,
    #                 "message": combined_message,
    #                 "explanation": check_result.explanation
    #             }
    #
    #         # If no missing information, proceed to generate code
    #         return self.generate_code_from_requirements(design_requirements, context_from_param_check, request_origin)
    #
    #     except Exception as e:
    #         import traceback
    #         error_traceback = traceback.format_exc()
    #         print(f"[ERROR] Error during requirements analysis: {e}")
    #         print(f"Traceback: {error_traceback}")
    #         return {"error": f"Error analyzing requirements: {str(e)}", "code": None, "gltf_path": None}

    def continue_information_collection(self, combined_text, request_origin='unknown', session_id=None, force_proceed=False): # Added session_id
        """
        Continue collecting information or proceed if forced/max attempts reached.

        """
        # Ensure session_id is present
        if session_id is None:
            # This should ideally not happen if called correctly from process_request
            print("ERROR: session_id is None in continue_information_collection. Generating a new one.")
            import uuid
            import random
            rand_digits = random.randint(100000, 999999)
            rand_uuid = uuid.uuid4().hex[:6]
            session_id = f"session_{rand_uuid}_{rand_digits}"

        state = self._get_session_state(session_id) # Get session state

        try:
            current_user_text_for_chain = combined_text # `combined_text` is " ".join(state['user_text_history'])

            chain_input = {
                "user_text": current_user_text_for_chain, # This will now be the evolving conversation
                "previous_responses_formatted": self.format_previous_responses_for_session(state['previous_responses']),
                "latest_requirements_for_guidance": state['latest_requirements']
            }
            unified_chain_result = self.unified_processing_chain.invoke(chain_input)

            # unified_output = unified_chain_result["unified_output"] # OLD
            unified_output_obj = unified_chain_result["unified_output_obj"] # NEW
            raw_unified_json = unified_chain_result["raw_unified_json"] # NEW
            retrieved_context_for_code_gen = unified_chain_result["retrieved_context_for_code_gen"]

            # print(f"\n[SUCCESS] Re-analyzed with new info. Unified Output: {json.dumps(unified_output.dict(), indent=2)}") # OLD
            print(f"\n[SUCCESS] Re-analyzed with new info. Parsed Unified Output: {json.dumps(unified_output_obj.dict(), indent=2)}") # NEW
            # self.latest_requirements = unified_output_obj # Update with the latest analysis
            self._update_session_state(session_id, latest_requirements=unified_output_obj)


            # Handle parsing error first
            if unified_output_obj.title == "Unable to parse requirements": # MODIFIED
                print(f"[ERROR] Parsing error in continue_information_collection for session {session_id}: {unified_output_obj.explanation}") # MODIFIED
                # Reset conversation state to allow a fresh attempt or different query from user
                # self.conversation_state = "initial"
                self._update_session_state(session_id, conversation_state="initial")
                # state['previous_responses'].pop() # Optionally remove the last problematic response from history
                # state['user_text_history'].pop()
                return {
                    "code": None, "gltf_path": None,
                    "message": f"I had trouble understanding your latest information. Could you please try rephrasing it or provide different details? Error: {unified_output_obj.explanation}", # MODIFIED
                    "explanation": unified_output_obj.explanation # MODIFIED
                }

            # If still missing info (and parsing was successful) and haven't exceeded attempts (and not forced to proceed)
            if unified_output_obj.missing_info and unified_output_obj.questions and \
               len(state['previous_responses']) < self.MAX_QUESTION_ATTEMPTS and not force_proceed:

                # The questions from unified_output_obj.questions are the ones currently deemed missing by the LLM,
                # considering previous responses. These are the questions we should ask.
                # self.pending_questions = unified_output_obj.questions # Directly update with the LLM's current list
                self._update_session_state(session_id, pending_questions=unified_output_obj.questions)


                print(f"\n❓ Still missing information for session {session_id}. Asking questions: {state['pending_questions']}")

                all_questions_str = "\n".join([f"{i+1}. {q}" for i, q in enumerate(state['pending_questions'])])
                message_to_user = f"I need a bit more information:\n\n{all_questions_str}"
                if unified_output_obj.explanation:
                    message_to_user += f"\n\n{unified_output_obj.explanation}"

                return {
                    "code": None, "gltf_path": None,
                    "message": message_to_user, "explanation": unified_output_obj.explanation
                }

            # Proceed to generate code if no more questions, max attempts reached, or forced
            print(f"\n[WARNING] Proceeding with available information to generate code for session {session_id}.")
            # self.conversation_state = "generating_code" # Or "initial" if done
            self._update_session_state(session_id, conversation_state="generating_code")
            # Pass raw_unified_json to generate_code_from_requirements
            return self.generate_code_from_requirements(unified_output_obj, raw_unified_json, retrieved_context_for_code_gen, request_origin, session_id=session_id) # MODIFIED + Added session_id

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"[ERROR] Error during continue_information_collection for session {session_id}: {e}")
            print(f"Traceback: {error_traceback}")
            return {"error": f"Error processing your information: {str(e)}", "code": None, "gltf_path": None}

    def generate_code_from_requirements(self, design_requirements_obj: AnalysisAndParameterCheckOutput, raw_design_requirements_json: str, retrieved_context: str, request_origin='unknown', session_id=None):
        """Generate FreeCAD code from analyzed requirements, using pre-fetched context."""
        if session_id is None:
            print("ERROR: session_id is None in generate_code_from_requirements. Cannot proceed.")
            return {"error": "Session ID missing in generate_code_from_requirements", "code": None, "gltf_path": None}

        state = self._get_session_state(session_id)
        print(f"\nGenerating FreeCAD code for session {session_id}...")
        try:
            generated_code = self.rag_code_generation_chain.invoke({
                "design_requirements_obj": design_requirements_obj,
                "raw_design_requirements_json": raw_design_requirements_json,
                "retrieved_context": retrieved_context
            })
            print(f"[SUCCESS] FreeCAD code generation successful for session {session_id}.")

            self._update_session_state(
                session_id,
                latest_code=generated_code,
                latest_title=design_requirements_obj.title,
                # latest_requirements is already updated by the caller
                conversation_state="initial",
                pending_questions=[],
                previous_responses=[],
                initiating_query_for_collection=None,
                user_text_history=[]
            )

            gltf_path, obj_path, step_path = self.save_outputs(generated_code, design_requirements_obj, request_origin=request_origin)
            return {
                "code": generated_code,
                "gltf_path": gltf_path,
                "obj_path": obj_path,
                "step_path": step_path,
                "message": "Code generated successfully."
            }

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"[ERROR] Error during code generation for session {session_id}: {e}\n{error_traceback}")
            return {"error": f"Error generating code: {e}", "code": None, "gltf_path": None}

    def process_edit_request(self, user_text, request_origin='unknown', session_id=None): # Added session_id
        """Process a request to edit existing FreeCAD code. Returns a dict."""
        if session_id is None:
            print("ERROR: session_id is None in process_edit_request. Cannot proceed.")
            return {"error": "Session ID missing in process_edit_request", "code": None, "gltf_path": None}

        state = self._get_session_state(session_id)

        if not state['latest_code']:
            print(f"[ERROR] No existing code to edit for session {session_id}. Please generate code first.")
            return {"error": "No existing code to edit. Please generate code first.", "code": None, "gltf_path": None}

        print(f"\n[DEBUG] Processing edit request for session {session_id}: '{user_text}'...")

        # If we're in the process of collecting information for an edit
        if state['conversation_state'] == "collecting_info_edit" and state['pending_questions']:
            combined_text = " ".join(state['user_text_history'])
            return self.continue_edit_information_collection(combined_text, request_origin, session_id=session_id)

        # Save this edit request to session history
        state['edit_request_history'].append(user_text) # Uses session state

        # Retrieve context for the edit request
        try:
            # Create a query based on the edit request
            if self.local_retriever:
                retrieved_docs = self.local_retriever.invoke(user_text)
                retrieved_context = format_retrieved_context(retrieved_docs)
            else:
                retrieved_context = "No local retriever available."

            print("[SUCCESS] Context retrieval successful.")
        except Exception as e:
            print(f"[ERROR] Error during context retrieval: {e}")
            retrieved_context = "Error retrieving context."

        # Check if there's missing information in the edit request - Simplified for now
        # We will directly proceed to the code_editing_chain.
        # The `retrieved_context` from the RAG call above is used for the code_editing_chain.

        # Edit the code using the code editing chain
        try:
            print(f"\nEditing FreeCAD code for session {session_id}...")

            sanitized_title = ""
            if state['latest_title']:
                sanitized_title = re.sub(r'[^\w\s-]', '', state['latest_title']).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128).replace(' ', '_')

            if not sanitized_title and state['latest_requirements'] and state['latest_requirements'].title:
                sanitized_title = re.sub(r'[^\w\s-]', '', state['latest_requirements'].title).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128).replace(' ', '_')

            if not sanitized_title:
                sanitized_title = "edited_model"

            print(f"Using sanitized_title: '{sanitized_title}' for code editing in session {session_id}")

            edited_code = self.code_editing_chain.invoke({
                "original_code": state['latest_code'],
                "user_request": user_text,
                "retrieved_context": retrieved_context,
                "sanitized_title": sanitized_title
            })
            print(f"[SUCCESS] FreeCAD code editing successful for session {session_id}.")

            self._update_session_state(
                session_id,
                latest_code=edited_code,
                # latest_title might change based on edit, or stay same. For now, keep existing.
                # latest_requirements might also need update if edit implies new requirements.
                conversation_state="initial",
                pending_questions=[],
                previous_responses=[],
                initiating_query_for_collection=None
                # user_text_history is already updated at the start of process_request
            )

        except Exception as e:
            print(f"[ERROR] Error during code editing for session {session_id}: {e}")
            return {"error": f"Error editing code: {e}", "code": None, "gltf_path": None}

        gltf_path, obj_path = None, None
        current_requirements = state['latest_requirements'] if state['latest_requirements'] else DesignRequirements(
            title=state['latest_title'] or "Edited_Model",
            shapes=[ShapeRequirement(shape_type="unknown", dimensions={"unknown": 0.0})], # Ensure float
            complexity_level=1
        )

        gltf_path, obj_path, step_path = self.save_outputs(edited_code, current_requirements, base_filename=sanitized_title, request_origin=request_origin)

        # No need to update latest_code here again as it's done in _update_session_state
        print(f"[SUCCESS] Code updated successfully for session {session_id}.")

        # Reset user_text_history for this session for the next distinct request
        self._update_session_state(session_id, user_text_history=[])


        return {"code": edited_code, "gltf_path": gltf_path, "obj_path": obj_path, "step_path": step_path}

    def continue_edit_information_collection(self, combined_text, request_origin='unknown', session_id=None): # Added session_id
        """Continue collecting information for an edit request from the user."""
        if session_id is None:
            print("ERROR: session_id is None in continue_edit_information_collection. Cannot proceed.")
            return {"error": "Session ID missing", "code": None, "gltf_path": None}

        state = self._get_session_state(session_id)

        try:
            print(f"\n[WARNING] Proceeding with available information for edit in session {session_id}, even if incomplete")
            self._update_session_state(session_id, conversation_state="initial") # Mark as no longer collecting

            retrieved_context = state.get('edit_context', {}).get('retrieved_context', "No context available.")

            print(f"\nEditing FreeCAD code with available information for session {session_id}...")

            sanitized_title = ""
            if state['latest_title']:
                sanitized_title = re.sub(r'[^\\w\\s-]', '', state['latest_title']).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128).replace(' ', '_')
            if not sanitized_title and state['latest_requirements'] and state['latest_requirements'].title:
                sanitized_title = re.sub(r'[^\\w\\s-]', '', state['latest_requirements'].title).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128).replace(' ', '_')
            if not sanitized_title:
                sanitized_title = "edited_model"

            edited_code = self.code_editing_chain.invoke({
                "original_code": state['latest_code'],
                "user_request": combined_text,
                "retrieved_context": retrieved_context,
                "sanitized_title": sanitized_title
            })
            print(f"[SUCCESS] FreeCAD code editing successful for session {session_id}.")

            current_requirements = state['latest_requirements'] if state['latest_requirements'] else DesignRequirements(
                title=state['latest_title'] or "Edited_Model",
                shapes=[ShapeRequirement(shape_type="unknown", dimensions={"unknown": 0.0})], # Ensure float
                complexity_level=1
            )
            gltf_path, obj_path, step_path = self.save_outputs(edited_code, current_requirements, base_filename=sanitized_title, request_origin=request_origin)

            self._update_session_state(
                session_id,
                latest_code=edited_code,
                conversation_state="initial",
                pending_questions=[],
                previous_responses=[],
                initiating_query_for_collection=None,
                user_text_history=[],
                edit_context={} # Clear edit context
            )

            return {"code": edited_code, "gltf_path": gltf_path, "obj_path": obj_path, "step_path": step_path}

        except Exception as e:
            print(f"[ERROR] Error during edit information collection: {e}")
            return {"error": f"Error processing your information for the edit: {e}", "code": None, "gltf_path": None}

    def save_outputs(self, code, design_requirements, base_filename="generated_cad", request_origin='unknown'):
        """
        Saves the generated code and requirements to files.
        Executes FreeCAD, converts OBJ to GLTF, and launches viewer.

        Args:
            code (str): The generated FreeCAD Python code
            design_requirements (DesignRequirements): The design requirements object
            base_filename (str): Base filename to use if title is empty
            request_origin (str): The origin of the request ('web', 'api', or 'unknown')

        Returns:
            str: The absolute path to the generated GLTF file if successful, otherwise None.
        """
        # Import file and path management utilities
        from src.utils.file_manager import save_code_file, save_metadata_file, execute_freecad_script, convert_obj_to_gltf
        from src.utils.path_manager import get_output_path, get_unique_filepath

        # Initialize return value
        gltf_file_path = None

        # Extract shape type and dimensions from design requirements
        shape_type = "unknown"
        dimensions = {}

        # Check if we have shapes in the design requirements
        if hasattr(design_requirements, 'shapes') and design_requirements.shapes:
            # Get the first shape's type
            primary_shape = design_requirements.shapes[0]
            shape_type = primary_shape.shape_type

            # Get the dimensions from the first shape
            if hasattr(primary_shape, 'dimensions') and primary_shape.dimensions:
                dimensions = primary_shape.dimensions

        # If we couldn't get dimensions from shapes, use fallbacks
        if not dimensions:
            # Use title as fallback for dimensions
            dimensions = design_requirements.title or base_filename

        # Use title as fallback for shape_type if needed
        if shape_type == "unknown" and design_requirements.title:
            shape_type = design_requirements.title

        # Save code file
        try:
            code_filepath = save_code_file(code, shape_type, dimensions, design_requirements.dict())
            print(f"💾 Saved generated code to: {code_filepath}")
        except Exception as e:
            print(f"[ERROR] Error saving code file: {e}")
            code_filepath = None

        # Save metadata file
        try:
            json_filepath = save_metadata_file(design_requirements.dict(), shape_type, dimensions, design_requirements.dict())
            print(f"💾 Saved design requirements to: {json_filepath}")
        except Exception as e:
            print(f"[ERROR] Error saving metadata file: {e}")
            json_filepath = None

        # Execute FreeCAD script
        if code_filepath:
            try:
                # Execute FreeCAD command
                success, output = execute_freecad_script(code_filepath)
                if success:
                    print(f"🚀 Successfully executed FreeCAD script: {code_filepath}")
                else:
                    print(f"[WARNING] Warning: FreeCAD script execution may have issues: {output}")
            except Exception as e:
                print(f"[ERROR] Error executing FreeCAD script: {e}")

        # Get expected output paths for OBJ, GLTF, and STEP files
        from src.utils.path_manager import OBJ_OUTPUT_DIR, GLTF_OUTPUT_DIR, CAD_OUTPUT_DIR, PROJECT_ROOT
        import datetime

        # Get today's date directory
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        obj_dir = OBJ_OUTPUT_DIR / today
        gltf_dir = GLTF_OUTPUT_DIR / today
        step_dir = CAD_OUTPUT_DIR / today

        # Create directories if they don't exist
        obj_dir.mkdir(parents=True, exist_ok=True)
        gltf_dir.mkdir(parents=True, exist_ok=True)
        step_dir.mkdir(parents=True, exist_ok=True)

        # Generate base filenames
        base_filename = f"{shape_type}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Expected output files
        obj_path = obj_dir / f"{base_filename}.obj"
        # gltf_path is set later after conversion
        step_path = step_dir / f"{base_filename}.step"

        # Check for generated OBJ file (FreeCAD might have created it with a different name)
        # Look for any recently created OBJ files
        import glob
        import time
        from pathlib import Path

        # Get all OBJ files in the project directory and parent directory
        recent_time = time.time() - 10  # Files created in the last 10 seconds
        obj_files = []

        # Check in the project directory
        for obj_file in glob.glob(str(PROJECT_ROOT / "**/*.obj"), recursive=True):
            if os.path.getmtime(obj_file) > recent_time:
                obj_files.append(obj_file)

        # Check in the parent directory (old location)
        parent_dir = PROJECT_ROOT.parent
        for obj_file in glob.glob(str(parent_dir / "**/*.obj"), recursive=True):
            if os.path.getmtime(obj_file) > recent_time:
                obj_files.append(obj_file)

        # If we found any OBJ files, copy the most recent one to our new location
        if obj_files:
            # Sort by modification time (newest first)
            obj_files.sort(key=lambda p: os.path.getmtime(p), reverse=True)
            found_obj_path = obj_files[0]

            print(f"[DEBUG] Found recently created OBJ file: {found_obj_path}")

            try:
                # Copy to our new location
                import shutil
                shutil.copy2(found_obj_path, obj_path)
                print(f"📋 Copied to organized location: {obj_path}")
            except Exception as e:
                print(f"[WARNING] Warning: Could not copy OBJ file: {e}")
                # Use the existing file
                obj_path = Path(found_obj_path)

            # Convert OBJ to GLTF
            if obj_path.exists():
                try:
                    success, gltf_result_path = convert_obj_to_gltf(obj_path)
                    if success and gltf_result_path:
                        print(f"[SUCCESS] Successfully converted OBJ to GLTF: {gltf_result_path}")
                        gltf_file_path = str(gltf_result_path)
                    else:
                        print(f"[WARNING] Warning: OBJ to GLTF conversion failed")
                except Exception as e:
                    print(f"[ERROR] Error during OBJ to GLTF conversion: {e}")
        else:
            print(f"[WARNING] Warning: No recently created OBJ files found")

        # Check for generated STEP file (FreeCAD might have created it with a different name)
        # Look for any recently created STEP files
        step_files = []

        # Increase the time window for finding recently created files
        recent_time = time.time() - 30  # Files created in the last 30 seconds (increased from 10)

        # First, specifically check the cad_outputs_generated directory (most likely location)
        cad_outputs_dir = PROJECT_ROOT / "outputs" / "code" / "cad_outputs_generated"
        print(f"[DEBUG] Specifically checking for STEP files in: {cad_outputs_dir}")
        if cad_outputs_dir.exists():
            for step_file in glob.glob(str(cad_outputs_dir / "*.step")):
                # Don't filter by time for files in this specific directory
                step_files.append(step_file)
                print(f"[DEBUG] Found STEP file in cad_outputs_generated: {step_file}, modified: {os.path.getmtime(step_file)}, recent threshold: {recent_time}")

        # If no files found in the specific directory, check more broadly
        if not step_files:
            print("[DEBUG] No STEP files found in cad_outputs_generated, checking project directory...")
            # Check in the project directory
            for step_file in glob.glob(str(PROJECT_ROOT / "**/*.step"), recursive=True):
                if os.path.getmtime(step_file) > recent_time:
                    step_files.append(step_file)
                    print(f"[DEBUG] Found STEP file in project directory: {step_file}")

            # Check in the parent directory (old location)
            print("[DEBUG] Checking parent directory for STEP files...")
            for step_file in glob.glob(str(parent_dir / "**/*.step"), recursive=True):
                if os.path.getmtime(step_file) > recent_time:
                    step_files.append(step_file)
                    print(f"[DEBUG] Found STEP file in parent directory: {step_file}")

        # If we found any STEP files, copy the most recent one to our new location
        if step_files:
            # Sort by modification time (newest first)
            step_files.sort(key=lambda p: os.path.getmtime(p), reverse=True)
            found_step_path = step_files[0]

            print(f"[DEBUG] Found recently created STEP file: {found_step_path}")

            try:
                # Copy to our new location
                import shutil
                shutil.copy2(found_step_path, step_path)
                print(f"📋 Copied to organized location: {step_path}")

                # Launch viewer only if the request originated from the web
                if request_origin == 'web':
                    print(f"🚀 Launching STEP viewer...")
                    try:
                        # Use sys.executable to ensure the same Python env is used
                        # Use Popen for non-blocking execution
                        # Use the wrapper script at the root directory
                        gui_script_path = os.path.abspath("gui_step.py") # Ensure absolute path

                        # Check if the wrapper script exists, otherwise use the direct path
                        if not os.path.exists(gui_script_path):
                            # Try to use the direct path to the script in the Tolery directory
                            gui_script_path = os.path.abspath(os.path.join("Tolery", "gui_step.py"))
                            print(f"Using direct path to STEP viewer: {gui_script_path}")

                        subprocess.Popen([sys.executable, gui_script_path, str(step_path)]) # Use absolute path
                        print("[SUCCESS] STEP viewer launched.")
                    except Exception as e:
                        print(f"[ERROR] Error launching STEP viewer: {e}")
                else:
                    print("[INFO] Skipping STEP viewer launch for non-web request.")
            except Exception as e:
                print(f"[WARNING] Warning: Could not copy STEP file: {e}")
                # Use the existing file for the viewer
                step_path = Path(found_step_path)
        else:
            print(f"[WARNING] Warning: No recently created STEP files found")

            # Fallback: Check if there's a STEP file with the same name as the OBJ file
            if obj_path and obj_path.exists():
                potential_step_path = Path(str(obj_path).replace('.obj', '.step'))
                if potential_step_path.exists():
                    print(f"[DEBUG] Fallback: Found STEP file with matching name: {potential_step_path}")
                    try:
                        # Copy to our new location
                        import shutil
                        shutil.copy2(potential_step_path, step_path)
                        print(f"📋 Copied to organized location: {step_path}")
                    except Exception as e:
                        print(f"[WARNING] Warning: Could not copy fallback STEP file: {e}")
                        step_path = potential_step_path

            # Fallback 2: Check if there's a STEP file with the sanitized title
            if design_requirements and design_requirements.title:
                sanitized_title = re.sub(r'[^\w\s-]', '', design_requirements.title).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128)
                sanitized_title = sanitized_title.replace(' ', '_')

                if sanitized_title:
                    for potential_dir in [cad_outputs_dir, PROJECT_ROOT / "outputs" / "code"]:
                        if potential_dir.exists():
                            for step_file in glob.glob(str(potential_dir / f"{sanitized_title}*.step")):
                                print(f"[DEBUG] Fallback 2: Found STEP file matching title: {step_file}")
                                try:
                                    # Copy to our new location
                                    import shutil
                                    shutil.copy2(step_file, step_path)
                                    print(f"📋 Copied to organized location: {step_path}")
                                    break  # Exit the loop once we've found and copied a file
                                except Exception as e:
                                    print(f"[WARNING] Warning: Could not copy title-matched STEP file: {e}")
                                    step_path = Path(step_file)

            # Fallback 3: Last resort - just take any STEP file in the cad_outputs_generated directory
            if cad_outputs_dir.exists():
                all_step_files = list(cad_outputs_dir.glob("*.step"))
                if all_step_files:
                    newest_step = max(all_step_files, key=lambda p: p.stat().st_mtime)
                    print(f"[DEBUG] Fallback 3: Using newest STEP file in directory: {newest_step}")
                    try:
                        # Copy to our new location
                        import shutil
                        shutil.copy2(newest_step, step_path)
                        print(f"📋 Copied to organized location: {step_path}")
                    except Exception as e:
                        print(f"[WARNING] Warning: Could not copy newest STEP file: {e}")
                        step_path = newest_step

        # Log all generated files for reference
        print("\n📁 Generated Files Summary:")
        if code_filepath:
            print(f"  - Python Code: {code_filepath}")
        if json_filepath:
            print(f"  - JSON Requirements: {json_filepath}")
        if step_path and step_path.exists():
            print(f"  - STEP Model: {step_path}")
        if gltf_file_path:
            print(f"  - GLTF Model: {gltf_file_path}")
        if obj_path and obj_path.exists():
            print(f"  - OBJ Model: {obj_path}")
        print("")

        # Return the GLTF file path, OBJ path, and STEP path if available
        obj_path_to_return = str(obj_path) if obj_path and obj_path.exists() else None
        step_path_to_return = str(step_path) if step_path and step_path.exists() else None
        return gltf_file_path, obj_path_to_return, step_path_to_return

# Removed if __name__ == "__main__": block
